import { YellowstoneGeyserClient } from '../src/clients/yellowstone'

const client = new YellowstoneGeyserClient('https://solana-yellowstone-grpc.publicnode.com')

console.log('Version', await client.getVersion({}))

const stream = client.createStream()

stream.on('subscribed', () => {
    console.log('Stream subscribed')
})

stream.on('error', (error) => {
    console.error('Stream error:', error)
})

stream.on('closed', () => {
    console.log('Stream closed')
})

stream.on('data', (data) => {
    console.log('Data received:', JSON.stringify(data))
})

await stream.subscribe()
